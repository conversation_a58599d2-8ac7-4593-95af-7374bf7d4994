import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';
import '../models/product_model.dart';
import '../services/product_service.dart';
import '../widgets/product_management/product_card_admin.dart';
import '../widgets/product_management/pending_product_card.dart';
import '../widgets/product_management/edit_product_modal.dart';
import '../widgets/common/loading_widget.dart';
import '../widgets/common/empty_state_widget.dart';
import 'product_detail_screen.dart';

class PendingProductsScreen extends StatefulWidget {
  const PendingProductsScreen({super.key});

  @override
  State<PendingProductsScreen> createState() => _PendingProductsScreenState();
}

class _PendingProductsScreenState extends State<PendingProductsScreen> {
  List<ProductModel> _pendingProducts = [];
  bool _isLoading = false;
  bool _hasMoreProducts = true;
  DocumentSnapshot? _lastDocument;
  String _searchQuery = '';
  String _selectedCategory = 'All';
  final TextEditingController _searchController = TextEditingController();
  final Set<String> _selectedProducts = {};
  bool _isSelectionMode = false;

  @override
  void initState() {
    super.initState();
    _loadPendingProducts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Pending Products (${_pendingProducts.length})',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppConstants.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Back',
        ),
        actions: [
          if (_isSelectionMode) ...[
            IconButton(
              icon: const Icon(Icons.check_circle, color: Colors.white),
              onPressed: _selectedProducts.isNotEmpty ? _bulkApproveProducts : null,
              tooltip: 'Approve Selected',
            ),
            IconButton(
              icon: const Icon(Icons.cancel, color: Colors.white),
              onPressed: _selectedProducts.isNotEmpty ? _bulkRejectProducts : null,
              tooltip: 'Reject Selected',
            ),
            IconButton(
              icon: const Icon(Icons.close, color: Colors.white),
              onPressed: _exitSelectionMode,
              tooltip: 'Exit Selection',
            ),
          ] else ...[
            IconButton(
              icon: const Icon(Icons.checklist, color: Colors.white),
              onPressed: _pendingProducts.isNotEmpty ? _enterSelectionMode : null,
              tooltip: 'Select Multiple',
            ),
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: _refreshProducts,
              tooltip: 'Refresh',
            ),
          ],
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilter(),
          if (_isSelectionMode) _buildSelectionStatistics(),
          Expanded(
            child: _buildProductsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: AppConstants.borderColor),
        ),
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search pending products...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                        _refreshProducts();
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _debounceSearch();
            },
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          // Category filter
          Row(
            children: [
              const Text(
                'Category: ',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: AppConstants.fontSizeMedium,
                ),
              ),
              Expanded(
                child: DropdownButton<String>(
                  value: _selectedCategory,
                  isExpanded: true,
                  items: ['All', 'Electronics', 'Fashion', 'Home', 'Books', 'Sports', 'Other']
                      .map((category) => DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedCategory = value;
                      });
                      _refreshProducts();
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSelectionStatistics() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: AppConstants.borderColor),
        ),
      ),
      child: _buildStatCard(
        'Selected',
        _selectedProducts.length.toString(),
        Icons.check_circle,
        AppConstants.primaryColor,
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: AppConstants.paddingSmall),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: TextStyle(
                  fontSize: AppConstants.fontSizeSmall,
                  color: color,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductsList() {
    if (_isLoading && _pendingProducts.isEmpty) {
      return const LoadingWidget();
    }

    if (_pendingProducts.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.pending_actions,
        title: 'No Pending Products',
        subtitle: 'All products have been reviewed or no products are waiting for approval.',
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshProducts,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _pendingProducts.length + (_hasMoreProducts ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _pendingProducts.length) {
            _loadMoreProducts();
            return const Padding(
              padding: EdgeInsets.all(AppConstants.paddingMedium),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          final product = _pendingProducts[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
            child: GestureDetector(
              onTap: _isSelectionMode
                  ? () => _toggleProductSelection(product.id)
                  : () => _navigateToProductDetail(product),
              child: Stack(
                children: [
                  PendingProductCard(
                    product: product,
                    onProductUpdated: () {
                      _refreshProducts();
                    },
                    isSelectionMode: _isSelectionMode,
                    isSelected: _selectedProducts.contains(product.id),
                    onSelectionChanged: (selected) {
                      if (selected) {
                        _selectedProducts.add(product.id);
                      } else {
                        _selectedProducts.remove(product.id);
                      }
                      setState(() {});
                    },
                    onEdit: () => _editProduct(product),
                  ),
                  // Pending status overlay
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppConstants.warningColor,
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                      ),
                      child: const Text(
                        'PENDING',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: AppConstants.fontSizeSmall,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // Navigation and actions
  Future<void> _navigateToProductDetail(ProductModel product) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ProductDetailScreen(product: product),
      ),
    );

    if (result != null) {
      _refreshProducts();
    }
  }

  void _enterSelectionMode() {
    setState(() {
      _isSelectionMode = true;
      _selectedProducts.clear();
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedProducts.clear();
    });
  }

  void _toggleProductSelection(String productId) {
    setState(() {
      if (_selectedProducts.contains(productId)) {
        _selectedProducts.remove(productId);
      } else {
        _selectedProducts.add(productId);
      }
    });
  }

  // Data loading methods
  Future<void> _loadPendingProducts() async {
    if (_isLoading || !_hasMoreProducts) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final products = await ProductService.getPendingProductsPaginated(
        lastDocument: _lastDocument,
        category: _selectedCategory == 'All' ? null : _selectedCategory,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
      );

      if (mounted) {
        setState(() {
          if (_lastDocument == null) {
            _pendingProducts = products;
          } else {
            _pendingProducts.addAll(products);
          }
          _hasMoreProducts = products.length >= 20;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      print('Error loading pending products: $e');
    }
  }

  Future<void> _loadMoreProducts() async {
    if (!_isLoading && _hasMoreProducts) {
      await _loadPendingProducts();
    }
  }

  Future<void> _refreshProducts() async {
    setState(() {
      _lastDocument = null;
      _hasMoreProducts = true;
      _pendingProducts.clear();
    });
    await _loadPendingProducts();
  }

  void _debounceSearch() {
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_searchController.text == _searchQuery) {
        _refreshProducts();
      }
    });
  }

  // Bulk operations
  Future<void> _bulkApproveProducts() async {
    if (_selectedProducts.isEmpty) return;

    final confirmed = await _showConfirmationDialog(
      'Approve Products',
      'Are you sure you want to approve ${_selectedProducts.length} products?',
    );

    if (confirmed) {
      await _performBulkOperation(
        'Approving products...',
        (productId) => ProductService.approveProduct(
          productId: productId,
          moderatorNote: 'Bulk approved by admin',
        ),
      );
    }
  }

  Future<void> _bulkRejectProducts() async {
    if (_selectedProducts.isEmpty) return;

    final TextEditingController noteController = TextEditingController();
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Products'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Are you sure you want to reject ${_selectedProducts.length} products?'),
            const SizedBox(height: AppConstants.paddingMedium),
            TextField(
              controller: noteController,
              decoration: const InputDecoration(
                labelText: 'Rejection reason (optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(noteController.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (result != null) {
      await _performBulkOperation(
        'Rejecting products...',
        (productId) => ProductService.rejectProduct(
          productId: productId,
          moderatorNote: result.isNotEmpty ? result : 'Bulk rejected by admin',
        ),
      );
    }
  }

  Future<void> _performBulkOperation(
    String loadingMessage,
    Future<bool> Function(String) operation,
  ) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: AppConstants.paddingMedium),
            Text(loadingMessage),
          ],
        ),
      ),
    );

    try {
      int successCount = 0;
      for (final productId in _selectedProducts) {
        final success = await operation(productId);
        if (success) successCount++;
      }

      Navigator.of(context).pop(); // Close loading dialog

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$successCount products processed successfully'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        _exitSelectionMode();
        _refreshProducts();
      }
    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error processing products: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  Future<bool> _showConfirmationDialog(String title, String message) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  void _editProduct(ProductModel product) {
    showDialog(
      context: context,
      builder: (context) => EditProductModal(
        product: product,
        onProductUpdated: () {
          _refreshProducts();
        },
      ),
    );
  }
}
